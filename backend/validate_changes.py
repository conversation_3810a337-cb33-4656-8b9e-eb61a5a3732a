#!/usr/bin/env python3
"""
Simple validation script to check our code changes are syntactically correct
and follow the expected patterns for team-based access control.
"""

import ast
import os
import sys


def validate_file_syntax(file_path):
    """Check if a Python file has valid syntax"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)


def check_access_control_changes():
    """Validate that our access control changes are present"""
    print("🔍 Validating access control changes...")
    
    # Check backend/onyx/access/access.py
    access_file = "onyx/access/access.py"
    print(f"\n📁 Checking {access_file}...")
    
    valid, error = validate_file_syntax(access_file)
    if not valid:
        print(f"   ❌ Syntax error: {error}")
        return False
    
    with open(access_file, 'r') as f:
        content = f.read()
    
    # Check for our key changes
    checks = [
        ("Anonymous user check", "if not user:" in content),
        ("Team-based ACL", "user.user_groups" in content),
        ("Public document access", "is_public=True" in content),
    ]
    
    for check_name, condition in checks:
        if condition:
            print(f"   ✅ {check_name}: Found")
        else:
            print(f"   ❌ {check_name}: Missing")
            return False
    
    return True


def check_database_filter_changes():
    """Validate database filtering changes"""
    print("\n🔍 Validating database filter changes...")
    
    files_to_check = [
        "onyx/db/document_set.py",
        "onyx/db/connector_credential_pair.py", 
        "onyx/db/persona.py"
    ]
    
    for file_path in files_to_check:
        print(f"\n📁 Checking {file_path}...")
        
        valid, error = validate_file_syntax(file_path)
        if not valid:
            print(f"   ❌ Syntax error: {error}")
            return False
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check for team-based filtering
        team_filtering_indicators = [
            "User__UserGroup" in content,
            "DocumentSet__UserGroup" in content,
            "UserGroup__ConnectorCredentialPair" in content,
            "Persona__UserGroup" in content,
            "user.id" in content and "UserGroup" in content
        ]

        if any(team_filtering_indicators):
            print("   ✅ Team-based filtering: Found")
        else:
            print("   ❌ Team-based filtering: Missing")
            return False
    
    return True


def check_api_changes():
    """Validate API endpoint changes"""
    print("\n🔍 Validating API endpoint changes...")
    
    files_to_check = [
        "onyx/server/features/document_set/api.py",
        "onyx/server/documents/connector.py",
        "onyx/server/documents/credential.py"
    ]
    
    for file_path in files_to_check:
        print(f"\n📁 Checking {file_path}...")
        
        valid, error = validate_file_syntax(file_path)
        if not valid:
            print(f"   ❌ Syntax error: {error}")
            return False
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check for public flag setting
        if "is_public = True" in content or "access_type = AccessType.PUBLIC" in content or "admin_public = True" in content:
            print("   ✅ Public flag setting: Found")
        else:
            print("   ❌ Public flag setting: Missing")
            return False
    
    return True


def check_frontend_changes():
    """Validate frontend form changes"""
    print("\n🔍 Validating frontend form changes...")
    
    files_to_check = [
        "../web/src/app/admin/connectors/[connector]/AddConnectorPage.tsx",
        "../web/src/app/admin/documents/sets/DocumentSetCreationForm.tsx",
        "../web/src/components/credentials/actions/CreateCredential.tsx",
        "../web/src/components/admin/connectors/CredentialForm.tsx"
    ]
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"   ⚠️  {file_path}: File not found (may be expected)")
            continue
            
        print(f"\n📁 Checking {file_path}...")
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check for public flag override
        if 'finalAccessType = "public"' in content or 'is_public: true' in content or 'admin_public: true' in content:
            print("   ✅ Public flag override: Found")
        else:
            print("   ❌ Public flag override: Missing")
            return False
    
    return True


def main():
    """Main validation function"""
    print("🔍 Validating team-based access control implementation...")
    print("=" * 60)
    
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    all_checks_passed = True
    
    # Run all validation checks
    checks = [
        check_access_control_changes,
        check_database_filter_changes,
        check_api_changes,
        check_frontend_changes
    ]
    
    for check in checks:
        try:
            if not check():
                all_checks_passed = False
        except Exception as e:
            print(f"   ❌ Error during validation: {e}")
            all_checks_passed = False
    
    print("\n" + "=" * 60)
    
    if all_checks_passed:
        print("🎉 All validation checks passed!")
        print("✅ Implementation appears to be syntactically correct")
        
        print("\n📋 Summary of validated changes:")
        print("   • Backend access control logic updated for team-based filtering")
        print("   • Database queries modified to use team relationships")
        print("   • API endpoints set all resources as public")
        print("   • Frontend forms override privacy toggles to send public")
        
        print("\n⚠️  Note: Full functional testing requires a complete environment")
        print("   Consider running integration tests when environment is available")
        
        return True
    else:
        print("❌ Some validation checks failed!")
        print("   Please review the errors above and fix any issues")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
