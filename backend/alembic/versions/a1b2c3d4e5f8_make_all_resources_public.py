"""make_all_resources_public

Revision ID: a1b2c3d4e5f8
Revises: a1b2c3d4e5f7
Create Date: 2025-01-09 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f8"
down_revision = "a1b2c3d4e5f7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Make cc_pairs and document sets public while preserving user_team relationships.
    This migration only updates connector_credential_pair and document_set tables
    to support the new team-based access control system.
    """
    connection = op.get_bind()

    # Update document_set table - set all to public
    print("Setting all document sets to public...")
    connection.execute(
        sa.text("UPDATE document_set SET is_public = true WHERE is_public = false")
    )

    # Update connector_credential_pair table - set all access_type to PUBLIC
    print("Setting all connector credential pairs to public access...")
    connection.execute(
        sa.text("UPDATE connector_credential_pair SET access_type = 'PUBLIC' WHERE access_type != 'PUBLIC'")
    )

    print("Migration completed: Document sets and CC pairs are now marked as public")
    print("Note: User team relationships have been preserved for access control")


def downgrade() -> None:
    """
    Revert cc_pairs and document sets back to their previous public/private states.
    WARNING: This migration cannot restore the original public/private states
    since we don't track what they were before. This downgrade will set
    document sets and cc_pairs back to private/restricted access.
    """
    connection = op.get_bind()

    print("WARNING: Downgrade cannot restore original public/private states")
    print("Setting document sets and CC pairs to private/restricted access...")

    # Revert document_set table - set all to private
    print("Setting all document sets to private...")
    connection.execute(
        sa.text("UPDATE document_set SET is_public = false")
    )

    # Revert connector_credential_pair table - set all access_type to PRIVATE
    print("Setting all connector credential pairs to private access...")
    connection.execute(
        sa.text("UPDATE connector_credential_pair SET access_type = 'PRIVATE'")
    )

    print("Downgrade completed: Document sets and CC pairs are now marked as private")
    print("Note: User team relationships have been preserved")
