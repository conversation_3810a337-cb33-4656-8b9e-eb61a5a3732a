"""make_all_resources_public

Revision ID: a1b2c3d4e5f8
Revises: a1b2c3d4e5f7
Create Date: 2025-01-09 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f8"
down_revision = "a1b2c3d4e5f7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Make all resources public while preserving user_team relationships.
    This migration sets all is_public, access_type, admin_public, and curator_public
    fields to their public values without changing any user_team associations.
    """
    connection = op.get_bind()

    # Update document_set table - set all to public
    print("Setting all document sets to public...")
    connection.execute(
        sa.text("UPDATE document_set SET is_public = true WHERE is_public = false")
    )
    
    # Update connector_credential_pair table - set all access_type to PUBLIC
    print("Setting all connector credential pairs to public access...")
    connection.execute(
        sa.text("UPDATE connector_credential_pair SET access_type = 'PUBLIC' WHERE access_type != 'PUBLIC'")
    )
    
    # Update credential table - set all to admin_public and curator_public
    print("Setting all credentials to admin_public and curator_public...")
    connection.execute(
        sa.text("UPDATE credential SET admin_public = true WHERE admin_public = false")
    )
    connection.execute(
        sa.text("UPDATE credential SET curator_public = true WHERE curator_public = false")
    )
    
    # Update persona table - set all to public
    print("Setting all personas to public...")
    connection.execute(
        sa.text("UPDATE persona SET is_public = true WHERE is_public = false")
    )
    
    # Update llm_provider table - set all to public
    print("Setting all LLM providers to public...")
    connection.execute(
        sa.text("UPDATE llm_provider SET is_public = true WHERE is_public = false")
    )
    
    # Update api_key table - set all to public
    print("Setting all API keys to public...")
    connection.execute(
        sa.text("UPDATE api_key SET is_public = true WHERE is_public = false")
    )
    
    # Update custom_tool table - set all to public
    print("Setting all custom tools to public...")
    connection.execute(
        sa.text("UPDATE custom_tool SET is_public = true WHERE is_public = false")
    )
    
    # Update input_prompt table - set all to public
    print("Setting all input prompts to public...")
    connection.execute(
        sa.text("UPDATE inputprompt SET is_public = true WHERE is_public = false")
    )
    
    # Update document table - set all to public
    print("Setting all documents to public...")
    connection.execute(
        sa.text("UPDATE document SET is_public = true WHERE is_public = false")
    )
    
    print("Migration completed: All resources are now marked as public")
    print("Note: User team relationships have been preserved for access control")


def downgrade() -> None:
    """
    Revert all resources back to their previous public/private states.
    WARNING: This migration cannot restore the original public/private states
    since we don't track what they were before. This downgrade will set
    everything back to private/restricted access.
    """
    connection = op.get_bind()
    
    print("WARNING: Downgrade cannot restore original public/private states")
    print("Setting all resources to private/restricted access...")
    
    # Revert document_set table - set all to private
    print("Setting all document sets to private...")
    connection.execute(
        sa.text("UPDATE document_set SET is_public = false")
    )
    
    # Revert connector_credential_pair table - set all access_type to PRIVATE
    print("Setting all connector credential pairs to private access...")
    connection.execute(
        sa.text("UPDATE connector_credential_pair SET access_type = 'PRIVATE'")
    )
    
    # Revert credential table - set all to non-public
    print("Setting all credentials to non-public...")
    connection.execute(
        sa.text("UPDATE credential SET admin_public = false")
    )
    connection.execute(
        sa.text("UPDATE credential SET curator_public = false")
    )
    
    # Revert persona table - set all to private
    print("Setting all personas to private...")
    connection.execute(
        sa.text("UPDATE persona SET is_public = false")
    )
    
    # Revert llm_provider table - set all to private
    print("Setting all LLM providers to private...")
    connection.execute(
        sa.text("UPDATE llm_provider SET is_public = false")
    )
    
    # Revert api_key table - set all to private
    print("Setting all API keys to private...")
    connection.execute(
        sa.text("UPDATE api_key SET is_public = false")
    )
    
    # Revert custom_tool table - set all to private
    print("Setting all custom tools to private...")
    connection.execute(
        sa.text("UPDATE custom_tool SET is_public = false")
    )
    
    # Revert input_prompt table - set all to private
    print("Setting all input prompts to private...")
    connection.execute(
        sa.text("UPDATE inputprompt SET is_public = false")
    )
    
    # Revert document table - set all to private
    print("Setting all documents to private...")
    connection.execute(
        sa.text("UPDATE document SET is_public = false")
    )
    
    print("Downgrade completed: All resources are now marked as private/restricted")
    print("Note: User team relationships have been preserved")
